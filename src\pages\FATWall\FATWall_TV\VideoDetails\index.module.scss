.container {
  width: 100%;
  height: 100%;
  position: relative;
  color: white;
  background-color: #000;
  /* 允许内部元素滚动，但容器本身不滚动 */
  overflow: hidden;
}

.videoBackground {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: blur(5px);
    transform: scale(1.05);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.9));
  }
}

.backButton {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  font-size: 24px;
  margin-bottom: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  cursor: pointer;
}

.scrollableContent {
  position: relative;
  z-index: 2;
  padding: 20px 80px;
  height: calc(100vh - 150px);
  max-height: calc(100vh - 150px);
  margin-top: 150px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
}

/* 当为剧集视图时的布局 */
.dramaScrollableContent {
  position: absolute;
  top: 5vh;
  left: 2vh;
  right: 0;
  bottom: 0;
  margin-top: 0;
  max-height: unset;
  height: auto;
  // background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8) 100px, rgba(0, 0, 0, 0.9));
  border-radius: 16px 16px 0 0;
  padding-top: 40px;
}

.title {
  font-size: 72px;
  font-weight: bold;
  margin-bottom: 10px;
}

.infoRow {
  display: flex;
  align-items: center;
  text-align: center;
  line-height: 100%;
  gap: 10px;
  // margin-bottom: 15px;
  font-size: 30px;
}

.rating {
  background-color: #fff;
  font-family: MiSans;
  color: #07B551;
  border: 2px solid #07B551;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  justify-content: center;
  line-height: 100%;
  text-align: center;
  padding: 5px;
  border-radius: 10px;
}

.tag {
  border:2px solid #fff;
  padding: 4px 10px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  font-size: 22px;
}

.descriptionContainer {
  margin: 20px 0;
  position: relative;
  max-width: 60%;
}

.description {
  font-size: 30px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  max-height: 4.8em; // 限制高度为3行
  display: -webkit-box;
  -webkit-line-clamp: 3; // 限制为3行
  -webkit-box-orient: vertical;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: 1000px;
    -webkit-line-clamp: unset;
  }
}

.fullDescription {
  font-size: 30px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
}

.limitedDescription {
  font-size: 30px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
}

.toggleButton {
  color: #1890FF;
  cursor: pointer;
  display: inline-block;
  margin-left: 8px;
  user-select: none;

  &:hover {
    text-decoration: underline;
  }
}

.buttons {
  display: flex;
  margin-bottom: 50px;
  margin-top: 50px;
  gap: 15px;
}

.primaryButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #0070f3;
  border-radius: 12px;
  height: 140px;
  width: 255px;
  font-size: 30px;
  font-weight: bold;
}

.secondaryButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 140px;
  height: 140px;
  border-radius: 20%;
  border: 1px solid #fff;

  // background-color: rgba(255, 255, 255, 0.1);
  .text {
    padding: 10px;
    font-size: 33px;
  }
}

.castSection {
  margin: 20px 0;
}

.sectionTitle {
  font-size: 30px;
  margin-bottom: 15px;
  padding-left: 20px;
}

.castList {
  display: flex;
  gap: 50px;
  overflow-x: auto;
  padding-left: 20px;
  padding-bottom: 10px;
}

.castItem {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.castAvatar {
  width: 110px;
  height: 110px;
  border-radius: 50%;
  margin-bottom: 8px;
  object-fit: cover;
}

.castName {
  font-size: 24px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.castRole {
  font-size: 24px;
  opacity: 0.7;
  text-align: center;
}

.fileInfo {
  margin-top: 20px;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 8px;
}

.fileInfoItem {
  font-size: 24px;
  margin-bottom: 5px;
  opacity: 0.7;
}

/* 下载弹窗样式 */
.downloadModal {
  :global(.ant-modal-content) {
    background-color: #FFFFFF;
    color: white;
    border-radius: 30px;
    height: 240px;
    width: 400px;
    padding: 0;
  }

  :global(.ant-modal-header) {
    background-color: #1f1f1f;
    // border-bottom: 1px solid #333;

    :global(.ant-modal-title) {
      color: white;
      font-weight: bold;
    }
  }

  :global(.ant-modal-close) {
    color: white;
  }

  :global(.ant-modal-body) {
    padding: 24px;

    p {
      color: #000;
      font-size: 16px;
      // text-align: center;
    }
  }

  :global(.ant-modal-footer) {
    // border-top: 1px solid #333;
    // padding: 16px 24px;
    border-radius: 30px;
    display: flex;
    // padding-bottom: 50px;
    justify-content: center;
  }
}

.downloadModalBtn {
  background-color: #F0F0F0;
  color: #000;
  border: none;
  border-radius: 4px;
  // padding: 8px 24px;
  font-size: 16px;
  width: 80%;
  margin-bottom: 50px;
  height: 60px;
  border-radius: 12px;
  transition: background-color 0.3s;

  &:hover {
    // background-color: #40a9ff;
  }
}

/* 更多菜单样式 */
.moreMenu {
  min-width: 140px;
  // background-color: #1f1f1f;
  border-radius: 4px;
  padding: 1px 0;
}

.moreMenuItem {
  padding: 8px 16px;
  font-size: 14px;
  color: #000;
  cursor: pointer;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* 剧集列表区域样式 */
.dramaInfo {
  margin: 30px 0;
}

.modal_button {
  width: 100%;
  height: 50px;
  background-color: var(--cancel-btn-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  border-radius: 16px;
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
  color: var(--text-color);
  cursor: pointer;

  &:hover {
    background-color: #fff;
    color: #4096ff;
  }
}

/* 当是剧集时，演员区域和文件信息的样式 */
.extraInfoSection {
  // margin-top: 60px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -30px;
    left: 0;
    width: 100%;
    height: 1px;
  }
}

// TVFocusable 焦点样式
.focus_item {
  &:focus {
    outline: none;

    // 通用焦点效果
    &.primaryButton,
    &.secondaryButton,
    &.castItem {
      transform: scale(1.05);
      transition: transform 0.3s ease;
      position: relative;

      // 蓝色流光边框效果
      &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(45deg,
            #00bfff, #1e90ff, #4169e1, #0066ff,
            #00bfff, #1e90ff, #4169e1, #0066ff);
        background-size: 400% 400%;
        border-radius: 15px;
        z-index: -1;
        animation: streamingLight 2s ease-in-out infinite;
      }

      // 内层蓝色光晕
      &::after {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        background: linear-gradient(45deg,
            rgba(0, 191, 255, 0.3),
            rgba(30, 144, 255, 0.3),
            rgba(65, 105, 225, 0.3),
            rgba(0, 102, 255, 0.3));
        border-radius: 13px;
        z-index: -1;
        filter: blur(2px);
      }
    }

    // 返回按钮特殊样式
    &.backButton {
      transform: scale(1.05);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(45deg,
            #00bfff, #1e90ff, #4169e1, #0066ff,
            #00bfff, #1e90ff, #4169e1, #0066ff);
        background-size: 400% 400%;
        border-radius: 50%;
        z-index: -1;
        animation: streamingLight 2s ease-in-out infinite;
      }

      &::after {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        background: linear-gradient(45deg,
            rgba(0, 191, 255, 0.3),
            rgba(30, 144, 255, 0.3));
        border-radius: 50%;
        z-index: -1;
        filter: blur(1px);
      }
    }

    // 演员卡片特殊样式
    &.castItem {
      &::before {
        border-radius: 8px;
      }

      &::after {
        border-radius: 6px;
      }
    }

    // 版本选择器特殊样式
    &.versionSelectWrapper {
      transform: scale(1.02);

      .customVersionSelect {
        border-color: #4096ff;
        background-color: rgba(64, 150, 255, 0.1);
        box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.3);
      }

      .dropdownArrow {
        transform: rotate(180deg);
        color: #4096ff;
      }
    }
  }
}

// 流光动画
@keyframes streamingLight {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.episodeList{
  margin-bottom: 50px;
}

// 版本选择器样式 - 与PC端完全一致
.versionSelector {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 30px 0;
  padding-left: 20px;
}

.versionLabel {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28px;
  white-space: nowrap;
}

.versionSelectWrapper {
  outline: none;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 3px rgba(64, 150, 255, 0.3);
  }
}

.versionSelect {
  :global(.ant-select-selector) {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    font-size: 24px !important;
    height: 50px !important;
    padding: 8px 16px !important;
  }

  :global(.ant-select-selection-item) {
    color: white !important;
    font-size: 24px !important;
    line-height: 34px !important;
  }

  :global(.ant-select-arrow) {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 20px !important;
  }

  &:hover :global(.ant-select-selector) {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }

  :global(.ant-select-focused) :global(.ant-select-selector) {
    border-color: #4096ff !important;
    box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2) !important;
  }
}

// Select下拉菜单样式
:global(.ant-select-dropdown) {
  background-color: rgba(0, 0, 0, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;

  :global(.ant-select-item) {
    color: white !important;
    font-size: 24px !important;
    padding: 12px 16px !important;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }
  }

  :global(.ant-select-item-option-selected) {
    background-color: rgba(64, 150, 255, 0.2) !important;
    color: white !important;
  }
}

// 自定义版本选择器样式
.customVersionSelect {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 20px;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 400px;

  &:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.15);
  }
}

.versionDisplay {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  color: white;
  font-size: 24px;
}

.dropdownArrow {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  transition: transform 0.3s ease;
}

// 版本选择弹窗样式
.versionModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.versionModalContent {
  background-color: rgba(20, 20, 20, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  min-width: 500px;
  max-width: 600px;
  max-height: 70vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.versionModalHeader {
  padding: 20px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(255, 255, 255, 0.05);

  h3 {
    margin: 0;
    color: white;
    font-size: 28px;
    font-weight: 600;
    text-align: center;
  }
}

.versionModalBody {
  padding: 20px 0 70px 0; // 增加底部padding，避免最后一个选项被提示文字挡住
  max-height: 400px;
  overflow-y: auto;

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
}

.versionOption {
  padding: 15px 30px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
}

.versionOptionSelected {
  background-color: rgba(64, 150, 255, 0.2);
  color: white;
  border-left-color: #4096ff;
  position: relative;

  &::before {
    content: '●';
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    color: #4096ff;
    font-size: 20px;
  }
}

.versionModalFooter {
  padding: 15px 30px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(255, 255, 255, 0.05);
}

.versionModalHint {
  color: rgba(255, 255, 255, 0.6);
  font-size: 18px;
  text-align: center;
}